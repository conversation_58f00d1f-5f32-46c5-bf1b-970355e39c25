/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import BaseCommand from '#src/core/BaseCommand.js';
import type Context from '#src/core/CommandContext/Context.js';
import AchievementService from '#src/services/AchievementService.js';
import { UIComponents } from '#src/utils/DesignSystem.js';
import { ApplicationCommandOptionType } from 'discord.js';

/**
 * Command for users to mark that they've answered a help request in a hub
 */
export default class HelpResponseCommand extends BaseCommand {
  constructor() {
    super({
      name: 'help-response',
      description: '🦸 Mark that you\'ve answered a help request in a hub',
      types: { slash: true },
      options: [
        {
          name: 'message-link',
          description: 'Link to the message you responded to',
          type: ApplicationCommandOptionType.String,
          required: true,
        },
      ],
    });
  }

  async execute(ctx: Context): Promise<void> {
    await ctx.deferReply({ flags: ['Ephemeral'] });

    const messageLink = ctx.options.getString('message-link', true);

    // Validate message link format (simple validation)
    if (!messageLink.includes('discord.com/channels/')) {
      const ui = new UIComponents(ctx.client);
      const container = ui.createErrorMessage(
        'Invalid Message Link',
        'Please provide a valid Discord message link. Right-click on a message and select "Copy Message Link".',
      );
      await ctx.editReply({ components: [container], flags: ['IsComponentsV2'] });
      return;
    }

    // Track help response for Hub Hero achievement
    const achievementService = new AchievementService();
    await achievementService.processEvent(
      'help_response',
      {
        userId: ctx.user.id,
      },
      ctx.client,
    );

    // Create UI response
    const ui = new UIComponents(ctx.client);
    const container = ui.createSuccessMessage(
      'Help Response Recorded',
      'Thank you for helping others in the hub! Your contribution has been recorded.',
    );

    await ctx.editReply({ components: [container], flags: ['IsComponentsV2'] });
  }
}
