/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import BaseCommand from '#src/core/BaseCommand.js';
import type Context from '#src/core/CommandContext/Context.js';
import AchievementService from '#src/services/AchievementService.js';
import { UIComponents } from '#src/utils/DesignSystem.js';
import { checkIfStaff } from '#src/utils/Utils.js';
import { ApplicationCommandOptionType } from 'discord.js';

/**
 * Command for moderators to award special achievements to users
 */
export default class AwardCommand extends BaseCommand {
  constructor() {
    super({
      name: 'award',
      description: '🏆 Award special achievements to users (moderator only)',
      types: { slash: true },
      staffOnly: true, // Only staff can use this command
      options: [
        {
          name: 'achievement',
          description: 'The achievement to award',
          type: ApplicationCommandOptionType.String,
          required: true,
          choices: [
            { name: 'Peacekeeper - Reported issue that improved hub safety', value: 'peacekeeper' },
            {
              name: 'Bridge Booster - Helped troubleshoot hub connection issue',
              value: 'bridge-booster',
            },
          ],
        },
        {
          name: 'user',
          description: 'The user to award the achievement to',
          type: ApplicationCommandOptionType.User,
          required: true,
        },
        {
          name: 'reason',
          description: 'Reason for awarding this achievement',
          type: ApplicationCommandOptionType.String,
          required: true,
        },
      ],
    });
  }

  async execute(ctx: Context): Promise<void> {
    await ctx.deferReply({ flags: ['Ephemeral'] });

    // Check if user is staff
    if (!checkIfStaff(ctx.user.id)) {
      const ui = new UIComponents(ctx.client);
      const container = ui.createErrorMessage(
        'Permission Denied',
        'Only InterChat staff members can award special achievements.',
      );
      await ctx.editReply({ components: [container], flags: ['IsComponentsV2'] });
      return;
    }

    const achievementId = ctx.options.getString('achievement', true);
    const targetUser = await ctx.options.getUser('user', true);
    const reason = ctx.options.getString('reason', true);

    // Create achievement service
    const achievementService = new AchievementService();

    // Award the achievement based on type
    let success = false;
    let achievementName = '';

    switch (achievementId) {
      case 'peacekeeper':
        await achievementService.awardPeacekeeperAchievement(
          targetUser.id,
          ctx.user.id,
          ctx.client,
        );
        success = true;
        achievementName = 'Peacekeeper';
        break;

      case 'bridge-booster':
        await achievementService.awardBridgeBoosterAchievement(
          targetUser.id,
          ctx.user.id,
          ctx.client,
        );
        success = true;
        achievementName = 'Bridge Booster';
        break;
    }

    // Create UI response
    const ui = new UIComponents(ctx.client);

    if (success) {
      const container = ui.createSuccessMessage(
        'Achievement Awarded',
        `Successfully awarded the **${achievementName}** achievement to ${targetUser.username} for: ${reason}`,
      );
      await ctx.editReply({ components: [container], flags: ['IsComponentsV2'] });
    }
    else {
      const container = ui.createErrorMessage(
        'Error Awarding Achievement',
        'Failed to award the achievement. Please try again later.',
      );
      await ctx.editReply({ components: [container], flags: ['IsComponentsV2'] });
    }
  }
}
