/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import BaseCommand from '#src/core/BaseCommand.js';
import type Context from '#src/core/CommandContext/Context.js';
import { Pagination } from '#src/modules/Pagination.js';
import UserDbService from '#src/services/UserDbService.js';
import {
  buildAchievementsComponents,
  buildAchievementsEmbed,
  getUserAchievementData,
} from '#src/utils/AchievementUtils.js';
import {
  ApplicationCommandOptionType,
  EmbedBuilder,
} from 'discord.js';

/**
 * Command to view user achievements
 */
export default class AchievementsCommand extends BaseCommand {
  constructor() {
    super({
      name: 'achievements',
      description: '🏆 View your achievements or another user\'s achievements',
      types: { slash: true, prefix: true },
      options: [
        {
          type: ApplicationCommandOptionType.User,
          name: 'user',
          description: 'The user to view achievements for (defaults to yourself)',
          required: false,
        },
        {
          type: ApplicationCommandOptionType.String,
          name: 'view',
          description: 'Choose which achievements to view',
          required: false,
          choices: [
            { name: 'All', value: 'all' },
            { name: 'Unlocked', value: 'unlocked' },
            { name: 'Locked', value: 'locked' },
            { name: 'In Progress', value: 'progress' },
          ],
        },
      ],
    });
  }

  /**
   * Execute the achievements command
   * @param ctx Command context
   */
  async execute(ctx: Context): Promise<void> {
    await ctx.deferReply();

    // Get command options
    const targetUser = await ctx.options.getUser('user') ?? ctx.user;
    const viewOption = ctx.options.getString('view') ?? 'all';

    // Create service instance
    const userService = new UserDbService();

    // Ensure user exists in database
    await userService.upsertUser(
      targetUser.id,
      {
        name: targetUser.username,
        image: targetUser.displayAvatarURL(),
      },
    );

    // Get achievement data
    const achievementData = await getUserAchievementData(
      targetUser.id,
      viewOption,
    );

    // Create paginator
    const paginator = new Pagination(ctx.client);

    // Create pages for pagination
    const itemsPerPage = 10;
    const totalAchievements = achievementData.length;

    // If no achievements, show a message
    if (totalAchievements === 0) {
      const embed = new EmbedBuilder()
        .setTitle(`${targetUser.username}'s Achievements`)
        .setDescription('No achievements found in this category.')
        .setColor('#FFD700')
        .setThumbnail(targetUser.displayAvatarURL({ size: 128 }));

      await ctx.editReply({ embeds: [embed] });
      return;
    }

    // Create pages
    for (let i = 0; i < totalAchievements; i += itemsPerPage) {
      const pageNumber = Math.floor(i / itemsPerPage) + 1;

      const embed = await buildAchievementsEmbed(
        targetUser,
        achievementData,
        viewOption,
        pageNumber,
      );

      // Add filter dropdown for achievements
      const filterRow = buildAchievementsComponents(
        totalAchievements,
        viewOption,
        pageNumber,
      );

      paginator.addPage({ embeds: [embed], components: filterRow });
    }

    // Run the paginator with the filter dropdown
    await paginator.run(ctx.interaction, {
      idle: 180000, // 3 minutes
    });

    // Note: Filter changes are handled by the AchievementFilter interaction handler
    // in src/interactions/AchievementFilter.ts using the RegisterInteractionHandler decorator
  }
}
