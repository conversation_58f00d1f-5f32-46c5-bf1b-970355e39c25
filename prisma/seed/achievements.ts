// @ts-check

/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { PrismaClient } from '#src/generated/prisma/client/client.js';

/**
 * Seeds the database with default achievements
 */
export async function seedAchievements(prisma: PrismaClient): Promise<void> {
  // Delete existing achievements
  await prisma.achievement.deleteMany({});

  // Define all achievement details
  const achievements = [
    // Connection achievements
    {
      id: 'world-tour',
      name: 'World Tour',
      description: 'Chat in 10+ different servers to unlock a globe badge.',
      badgeEmoji: '🌎',
      badgeUrl: 'https://i.imgur.com/z75vFkE.png',
      threshold: 10,
    },
    {
      id: 'bridge-builder',
      name: 'Bridge Builder',
      description: 'Link your server to a hub for the first time.',
      badgeEmoji: '🌉',
      badgeUrl: 'https://i.imgur.com/4KC7B0G.png',
      threshold: 1,
    },
    {
      id: 'global-chatter',
      name: 'Global Chatter',
      description: 'Send 100+ messages across all hubs.',
      badgeEmoji: '💬',
      badgeUrl: 'https://i.imgur.com/kOUprRd.png',
      threshold: 100,
    },
    {
      id: 'message-marathoner',
      name: 'Message Marathoner',
      description: 'Send 1,000+ messages in total.',
      badgeEmoji: '📨',
      badgeUrl: 'https://i.imgur.com/8J9zqSX.png',
      threshold: 1000,
    },
    {
      id: 'streak-master',
      name: 'Streak Master',
      description: 'Send messages for 30 consecutive days.',
      badgeEmoji: '🔥',
      badgeUrl: 'https://i.imgur.com/wPUU22L.png',
      threshold: 30,
    },
    
    // Interaction achievements
    {
      id: 'cross-cultural-ambassador',
      name: 'Cross-Cultural Ambassador',
      description: 'Receive reactions from users in 5+ different servers.',
      badgeEmoji: '🤝',
      badgeUrl: 'https://i.imgur.com/f7HRJ0w.png',
      threshold: 5,
    },
    {
      id: 'chain-reaction',
      name: 'Chain Reaction',
      description: 'Trigger a conversation with 10+ replies from different servers.',
      badgeEmoji: '⛓️',
      badgeUrl: 'https://i.imgur.com/z3M1KyK.png',
      threshold: 10,
    },
    {
      id: 'social-butterfly',
      name: 'Social Butterfly',
      description: 'Have your message replied to by users in 5+ servers.',
      badgeEmoji: '🦋',
      badgeUrl: 'https://i.imgur.com/jfYb3wd.png',
      threshold: 5,
    },
    {
      id: 'welcome-wagon',
      name: 'Welcome Wagon',
      description: 'Be the first to greet a new server joining the hub.',
      badgeEmoji: '👋',
      badgeUrl: 'https://i.imgur.com/0WPnzw3.png',
      threshold: 1,
    },
    
    // Hub engagement achievements
    {
      id: 'hub-hopper',
      name: 'Hub Hopper',
      description: 'Participate in 3+ different hubs.',
      badgeEmoji: '🦘',
      badgeUrl: 'https://i.imgur.com/OXR0kzP.png',
      threshold: 3,
    },
    {
      id: 'interconnected',
      name: 'Interconnected',
      description: 'Join a hub that connects 10+ servers.',
      badgeEmoji: '🔄',
      badgeUrl: 'https://i.imgur.com/rnB5F2d.png',
      threshold: 10,
    },
    {
      id: 'peacekeeper',
      name: 'Peacekeeper',
      description: 'Report an issue that moderators resolve, improving hub safety.',
      badgeEmoji: '🛡️',
      badgeUrl: 'https://i.imgur.com/DpCYW1g.png',
      threshold: 1,
    },
    {
      id: 'hub-guardian',
      name: 'Hub Guardian',
      description: 'Be a moderator in a hub with 90%+ positive interactions (tracked via sentiment).',
      badgeEmoji: '🔰',
      badgeUrl: 'https://i.imgur.com/PEJq4h7.png',
      threshold: 90,
    },
    {
      id: 'hub-hero',
      name: 'Hub Hero',
      description: 'Answer 50+ help requests or FAQs in the hub.',
      badgeEmoji: '🦸',
      badgeUrl: 'https://i.imgur.com/WhqRnbN.png',
      threshold: 50,
    },
    
    // Special achievements
    {
      id: 'pioneer',
      name: 'Pioneer',
      description: 'Be among the first 100 users globally to join InterChat.',
      badgeEmoji: '🏆',
      badgeUrl: 'https://i.imgur.com/zqPHJRH.png',
      threshold: 1,
    },
    {
      id: 'echo-chamber',
      name: 'Echo Chamber',
      description: 'Your message is broadcast to 10+ servers in one hub.',
      badgeEmoji: '📣',
      badgeUrl: 'https://i.imgur.com/kTQWA6H.png',
      threshold: 10,
    },
    {
      id: 'intercompletionist',
      name: 'InterCompletionist',
      description: 'Unlock all other achievements.',
      badgeEmoji: '🏅',
      badgeUrl: 'https://i.imgur.com/HMjLtWg.png',
      threshold: 1,
      secret: true,
    },
    {
      id: 'golden-webhook',
      name: 'Golden Webhook',
      description: 'Celebrate InterChat\'s anniversary by being active during its birthday month.',
      badgeEmoji: '🪝',
      badgeUrl: 'https://i.imgur.com/rn3Ce2J.png',
      threshold: 1,
      secret: true,
    },
    {
      id: 'archive-explorer',
      name: 'Archive Explorer',
      description: 'View the oldest archived message in a hub.',
      badgeEmoji: '📚',
      badgeUrl: 'https://i.imgur.com/xvbtMsV.png',
      threshold: 1,
    },
    {
      id: 'bridge-booster',
      name: 'Bridge Booster',
      description: 'Help troubleshoot a hub connection issue (validated by moderators).',
      badgeEmoji: '🔧',
      badgeUrl: 'https://i.imgur.com/IKjKTBm.png',
      threshold: 1,
    },
    {
      id: 'polyglot',
      name: 'Polyglot',
      description: 'Use the bot in 3+ languages.',
      badgeEmoji: '🗣️',
      badgeUrl: 'https://i.imgur.com/AohcEc9.png',
      threshold: 3,
    },
    
    // Voting achievements
    {
      id: 'voter',
      name: 'Voter',
      description: 'Vote 10 times on Top.gg.',
      badgeEmoji: '🗳️',
      badgeUrl: 'https://i.imgur.com/ugUGLS9.png',
      threshold: 10,
    },
    {
      id: 'super-voter',
      name: 'Super Voter',
      description: 'Vote 100 times on Top.gg.',
      badgeEmoji: '⭐',
      badgeUrl: 'https://i.imgur.com/nJKumyG.png',
      threshold: 100,
    },
    
    // Hub creation achievements
    {
      id: 'hub-creator',
      name: 'Hub Creator',
      description: 'Create a hub.',
      badgeEmoji: '🏗️',
      badgeUrl: 'https://i.imgur.com/WHUzstp.png',
      threshold: 1,
    },
    {
      id: 'viral-hub',
      name: 'Viral Hub',
      description: 'Get more than 25 servers in your hub.',
      badgeEmoji: '📈',
      badgeUrl: 'https://i.imgur.com/X7sBfJQ.png',
      threshold: 25,
    },
    {
      id: 'hub-empire',
      name: 'Hub Empire',
      description: 'Achieve 100 servers in your hub.',
      badgeEmoji: '👑',
      badgeUrl: 'https://i.imgur.com/wpfkaO2.png',
      threshold: 100,
    },
  ];

  // Insert achievements
  console.log('Seeding achievements...');
  await prisma.achievement.createMany({
    data: achievements,
    skipDuplicates: true,
  });
  
  console.log(`Seeded ${achievements.length} achievements.`);
}

const prisma = new PrismaClient();

seedAchievements(prisma)
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
